import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { CommonMessage } from '../CommonMessages/CommonMessages';
import {
  AddGatewayConsumerDto,
  GateWayConsumerRateLimit,
  GateWayConsumerUsername,
  GateWayServiceName,
  GateWayConsumerApiKey,
} from './gateway.dto';
import { Regexs } from '../utils/constants';

@Injectable()
export class GatewayService {
  private readonly logger = new Logger(GatewayService.name, {
    timestamp: true,
  });
  url: string = '';
  constructor(private readonly httpService: HttpService) {
    this.url = process.env.GATEWAY_URL;
  }

  async createConsumer(payload: AddGatewayConsumerDto) {
    try {
      const requestData = {
        custom_id: payload.customId,
        username: payload.username,
      };
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.url}/consumers`, requestData).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(
              'error in catch at createConsumer',
              JSON.stringify(error.toJSON()),
            );
            const errorResponse = error.toJSON();
            throw errorResponse;
          }),
        ),
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.FetchSucess,
        result: data,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at createConsumer',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode:
          error && error.status
            ? error.status
            : HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          error && error.message ? error.message : CommonMessage.InternalError,
      };
    }
  }

  async assignConsumerkey(payload: GateWayConsumerUsername) {
    try {
      let requestData = undefined;
      if (
        payload.apiKey &&
        payload.apiKey.trim() &&
        Regexs.textWithoutSpace.test(payload.apiKey)
      ) {
        requestData = {
          key: payload.apiKey,
        };
      }
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.url}/consumers/${payload.username}/key-auth`,
            requestData,
          )
          .pipe(
            catchError((error: AxiosError) => {
              this.logger.error(
                'error in catch at assignConsumerkey',
                JSON.stringify(error.toJSON()),
              );
              const errorResponse = error.toJSON();
              throw errorResponse;
            }),
          ),
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.FetchSucess,
        result: data,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at assignConsumerkey',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode:
          error && error.status
            ? error.status
            : HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          error && error.message ? error.message : CommonMessage.InternalError,
      };
    }
  }

  async setConsumerRateLimit(payload: GateWayConsumerRateLimit) {
    try {
      const requestData = {
        name: 'rate-limiting',
        consumer: {
          username: payload.username,
        },
        config: {
          second: payload.second ? payload.second : 10,
          day: payload.day ? payload.day : 1000,
          // limit_by: "credential"
        },
      };
      // if (payload.service) {
      //   const getService = await this.getServiceByName({
      //     serviceName: payload.service,
      //   });
      //   if (getService.error || !getService.result || !getService.result.id) {
      //     return getService;
      //   }

      //   requestData['service'] = { id: getService.result.id };
      // }
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.url}/plugins`, requestData).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(
              'error in catch at setConsumerRateLimit',
              JSON.stringify(error.toJSON()),
            );
            const errorResponse = error.toJSON();
            throw errorResponse;
          }),
        ),
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.FetchSucess,
        result: { plan: payload.plan, ...data },
      };
    } catch (error) {
      this.logger.error(
        'error in catch at getServiceByName',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode:
          error && error.status
            ? error.status
            : HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          error && error.message ? error.message : CommonMessage.InternalError,
      };
    }
  }

  async getServiceByName(payload: GateWayServiceName) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.url}/services/${payload.serviceName}`)
          .pipe(
            catchError((error: AxiosError) => {
              this.logger.error(
                'error in catch at httpService of getServiceByName',
                JSON.stringify(error.toJSON()),
              );
              const errorResponse = error.toJSON();
              throw errorResponse;
            }),
          ),
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.FetchSucess,
        result: data,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at getServiceByName',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode:
          error && error.status
            ? error.status
            : HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          error && error.message ? error.message : CommonMessage.InternalError,
      };
    }
  }

  async deleteConsumerkey(payload: GateWayConsumerApiKey) {
    try {
      await firstValueFrom(
        this.httpService
          .delete(
            `${this.url}/consumers/${payload.username}/key-auth/${payload.apiKey}`,
          )
          .pipe(
            catchError((error: AxiosError) => {
              this.logger.error(
                'error in catch at deleteConsumerkey',
                JSON.stringify(error.toJSON()),
              );
              const errorResponse = error.toJSON();
              throw errorResponse;
            }),
          ),
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.Sucess,
        result: null,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at deleteConsumerkey',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode:
          error && error.status
            ? error.status
            : HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          error && error.message ? error.message : CommonMessage.InternalError,
      };
    }
  }
}
