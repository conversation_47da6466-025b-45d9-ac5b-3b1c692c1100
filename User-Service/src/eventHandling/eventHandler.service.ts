import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { ethers } from 'ethers';
import { DEPOSIT_CONTRACT_ABI } from './ABI/contract.abi';
import { initializeProvider } from '../utils/utils.ethers';
import { BlockNumberRepository } from './blockNumber.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { Transaction } from '../transaction/entity/transaction.entity';
import { AppRelayer } from '../appRelayer/entities/appRelayer.entity';
import { Application } from '../application/entities/application.entity';
import { Repository } from 'typeorm';
import { SocketClient } from '../socket/client/socket.client';
import { SocketEvents } from '../socket/server/socket.dto';
import { SocketClientProvider } from 'src/socket/client';
import Decimal from 'decimal.js';
import {
  BLOCK_DIFF_NUMBER,
  DEPOSIT_CONTRACT_ADDRESS,
} from 'src/utils/constants';
import { BasePodService } from '../common/base-pod.service';
import { PodCoordinatorService } from '../pod-coordinator/pod-coordinator.service';

@Injectable()
export class EventListenerService
  extends BasePodService
  implements OnModuleInit
{
  private readonly logger = new Logger(EventListenerService.name);
  private readonly socketClient: SocketClient;
  private readonly BLOCK_DIFF = BLOCK_DIFF_NUMBER;
  private readonly CONTRACT_ADDRESS = DEPOSIT_CONTRACT_ADDRESS;
  private cronJob: any;

  constructor(
    private readonly blockNumberRepository: BlockNumberRepository,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(AppRelayer)
    private appRelayerRepository: Repository<AppRelayer>,
    @InjectRepository(Application)
    private applicationRepository: Repository<Application>,
    protected readonly podCoordinatorService: PodCoordinatorService,
    private schedulerRegistry: SchedulerRegistry,
  ) {
    super(podCoordinatorService);
    this.socketClient = new SocketClientProvider().getClient();
  }

  private readonly CHAINS = [
    { chainId: 1, name: 'MAINNET' },
    { chainId: 11155111, name: 'SEPOLIA' },
    { chainId: 17000, name: 'HOLESKY' },
    { chainId: 97, name: 'BSC_TESTNET' },
    { chainId: 137, name: 'POLYGON_MAINNET' },
    { chainId: 80002, name: 'POLYGON_AMOY' },
    { chainId: 80069, name: 'BERACHAIN_BEPOLIA' },
    { chainId: 8453, name: 'BASE_MAINNET' },
    { chainId: 84532, name: 'BASE_SEPOLIA' },
  ];

  async onModuleInit() {
    // Set up watcher for leader status changes
    this.podCoordinatorService.onLeadershipChange((isLeader: boolean) => {
      if (isLeader) {
        this.startCronJob();
      } else {
        this.stopCronJob();
      }
    });

    // Initial setup based on current leader status
    if (this.isLeaderPod()) {
      this.startCronJob();
    }
  }

  private startCronJob() {
    if (this.cronJob) {
      return; // Job already running
    }

    this.cronJob = setInterval(async () => {
      this.logger.log('Running event fetch cron job on leader pod...');

      const results = await Promise.allSettled(
        this.CHAINS.map((chain) =>
          this.fetchPastEvents(chain.chainId, chain.name),
        ),
      );

      results.forEach((result, index) => {
        const chain = this.CHAINS[index];
        if (result.status === 'rejected') {
          this.logger.error(
            `Failed to fetch events for ${chain.name} (${chain.chainId}): ${result.reason}`,
          );
        } else {
          this.logger.log(`Fetched events for ${chain.name}`);
        }
      });
    }, 30000); // 30 seconds

    this.schedulerRegistry.addInterval(
      'fetchEventsFromAllChains',
      this.cronJob,
    );
  }

  private stopCronJob() {
    if (this.cronJob) {
      clearInterval(this.cronJob);
      this.schedulerRegistry.deleteInterval('fetchEventsFromAllChains');
      this.cronJob = null;
      this.logger.debug('Stopped event fetch cron job on non-leader pod');
    }
  }

  private async fetchPastEvents(chainId: number, chainName: string) {
    const providerData = await initializeProvider(chainId);
    if (!providerData) {
      this.logger.warn(`Provider not found for ${chainName}`);
      return;
    }

    const { chainProvider } = providerData;
    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      DEPOSIT_CONTRACT_ABI,
      chainProvider,
    );

    const latestBlock = await chainProvider.getBlockNumber();
    let startBlock =
      await this.blockNumberRepository.getLastProcessedBlock(chainId);

    if (startBlock === 0) {
      startBlock = latestBlock - this.BLOCK_DIFF;
    }

    this.logger.verbose(
      `Fetching past events for ${chainName} from block ${startBlock} to ${latestBlock}`,
    );

    try {
      const depositEvents = await contract.queryFilter(
        contract.filters.Deposit(),
        startBlock,
        latestBlock,
      );

      depositEvents.forEach(async (event: any) => {
        if (event?.args && event.args.length >= 3) {
          const { sender, amount, fundingKey } = event.args;
          this.logger.log(`[${chainName}] Past Deposit Event:
            - Sender: ${sender}
            - Amount: ${ethers.formatUnits(amount, 18)} ${chainName}
            - Funding Key: ${fundingKey}
            - Tx Hash: ${event.transactionHash}`);

          await this.handleDepositEvent(event, chainId, chainName);
        } else {
          this.logger.error(`Invalid event data: ${JSON.stringify(event)}`);
        }
      });

      // Update last processed block in DB
      await this.blockNumberRepository.updateLastProcessedBlock(
        chainId,
        latestBlock,
      );
    } catch (error) {
      this.logger.error(`[${chainName}] Error fetching past events:`, error);
    }
  }

  async handleDepositEvent(event: any, chainId: any, chainName: string) {
    try {
      if (!event?.args || event.args.length < 3) {
        this.logger.error('Invalid event data:', event);
        return;
      }

      const receiver = event.args[0];
      const amount = event.args[1];
      const fundingKey = event.args[2];

      const formattedAmount = ethers.formatEther(amount);

      this.logger.verbose(
        `Processing deposit from ${receiver} of ${formattedAmount} for fundingKey ${fundingKey}`,
      );

      const application = await this.applicationRepository.findOne({
        where: { id: fundingKey },
        relations: { user: true },
      });

      if (!application) {
        this.logger.warn(`No application found for fundingKey: ${fundingKey}`);
        return;
      }

      const appRelayer = await this.appRelayerRepository.findOne({
        where: { app: { id: application.id } },
      });

      if (!appRelayer) {
        this.logger.warn(
          `No AppRelayer found for application ID: ${application.id}`,
        );
        return;
      }

      const transaction = await this.transactionRepository.findOne({
        where: { hash: event.transactionHash },
      });

      if (transaction) {
        this.logger.error(
          `Transaction already exist with same tx hash: ${event.transactionHash}`,
        );
        return;
      }

      const updatedBalance = new Decimal(appRelayer.balance)
        .add(new Decimal(formattedAmount))
        .toFixed(18);

      appRelayer.balance = updatedBalance;

      const newTransaction = this.transactionRepository.create({
        hash: event.transactionHash,
        blockHash: event.blockHash,
        blockNumber: event.blockNumber,
        data: event.data,
        from: this.CONTRACT_ADDRESS,
        to: receiver,
        value: amount,
        txType: 'deposit',
        status: 'confirmed',
        chainId: chainId,
        app: application,
        service: 'relayer',
        receipt: event,
      });

      await this.transactionRepository.manager.transaction(async (manager) => {
        await manager.save(newTransaction);
        await manager.save(appRelayer);
      });

      this.logger.log(`[${chainName}] Deposit Processed:
      - Receiver: ${receiver}
      - Amount: ${formattedAmount} ${chainName}
      - Funding Key: ${fundingKey}
      - App ID: ${application.id}
      - AppRelayer Balance Updated: ${appRelayer.balance}
      - Tx Hash: ${event.transactionHash}`);

      const socketRequest = {
        email: application.user.email,
        data: appRelayer,
      };
      this.socketClient.eventGenerator(
        SocketEvents.APP_TRANSACTION_WEBHOOK,
        socketRequest,
      );
    } catch (error) {
      this.logger.error('Error processing deposit event:', error);
    }
  }
}
