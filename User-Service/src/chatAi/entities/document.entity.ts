import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  JoinColumn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';
import { ChatAiMessage } from './message.entity';

@Entity('chat_ai_documents')
export class ChatAiDocument {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  filename: string;

  @Column({ nullable: false })
  filesize: number;

  @Column({ nullable: false })
  contentType: string;

  @Column({
    nullable: false,
    default: 'uploading',
  })
  status: string; // uploading, parsing, embedding, indexing, ready, error

  @Column({ type: 'jsonb', nullable: true })
  parsedData: any;

  @Column({ type: 'jsonb', nullable: true })
  summary: any; // AI-generated summary and key insights

  @Column({ nullable: true })
  pageCount: number;

  @Column({ nullable: true })
  wordCount: number;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'text', nullable: true })
  indexId: string; // LlamaIndex vector index ID

  @Column({ nullable: false })
  userId: string; // Supabase user ID

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => ChatAi, (chatAi) => chatAi.documents, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'projectId' })
  project: ChatAi;

  @Column({ nullable: false })
  projectId: string; // Now references ChatAi (project) ID

  @OneToMany(() => ChatAiMessage, (message) => message.document)
  messages: ChatAiMessage[];
}
