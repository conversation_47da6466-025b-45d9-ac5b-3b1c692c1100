import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';

@Entity('chat_ai_credit_usage')
export class ChatAiCreditUsage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  userId: string; // Supabase user ID

  @Column({ nullable: false })
  actionType: string; // 'project_create', 'document_upload', 'chat_message'

  @Column({ nullable: true })
  actionId: number; // Reference to project/document/message ID

  @Column({ nullable: false, default: 1 })
  creditsUsed: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  // Relationships
  @ManyToOne(() => ChatAi, (chatAi) => chatAi.creditUsage, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'chatAiId' })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;
}
