// ../user/user.controller.ts
import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { UserService } from './user.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  ChangePasswordDto,
  CreateUserDto,
  SendMailDto,
  LoginUserDto,
  ResetPasswordDto,
  VerifyEmailDto,
  CheckUsernameDto,
  GetStartedDto,
} from './dto/user.dto';
import { AuthService } from '../auth/auth.service';
import { LocalAuthGuard } from '../auth/guards/local-auth.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from './entities/user.entity';

@ApiTags('User')
@Controller('users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly authService: AuthService,
  ) {}

  @Post('register')
  async register(@Body() createUserDto: CreateUserDto) {
    return this.userService.register(createUserDto);
  }

  @Post('check-username')
  async checkUsernameAvail(@Body() checkUsernameDto: CheckUsernameDto) {
    return this.userService.checkUsernameAvail(checkUsernameDto);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(
    @Request() req: { user: User },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Body() loginUserDto: LoginUserDto,
  ) {
    return this.authService.login(req.user);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('change-password')
  async changePassword(
    @Request() req: { user: User },
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    const userId = req.user.id;
    return this.userService.changePassword(userId, changePasswordDto);
  }

  @Post('verify-email')
  async verifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.userService.verifyEmail(verifyEmailDto);
  }

  @Post('resend-email-verify')
  async resendEmailVerify(@Body() sendMailDto: SendMailDto) {
    return this.userService.sendMail(sendMailDto, 'verifyToken');
  }

  @Post('forgot-password')
  async forgotPassword(@Body() sendMailDto: SendMailDto) {
    return this.userService.sendMail(sendMailDto, 'resetPassToken');
  }

  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.userService.resetPassword(resetPasswordDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('get-started')
  async getStarted(
    @Request() req: { user: User },
    @Body() getStartedDto: GetStartedDto,
  ) {
    const userId = req.user.id;
    return this.userService.getStarted(userId, getStartedDto);
  }
}
