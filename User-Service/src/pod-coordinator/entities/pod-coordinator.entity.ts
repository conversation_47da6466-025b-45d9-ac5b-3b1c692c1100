import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class PodCoordinator {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  key: string;

  @Column()
  leaderPodName: string;

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  lastHeartbeat: Date;

  @Column({ default: 30 })
  ttlSeconds: number;
}
