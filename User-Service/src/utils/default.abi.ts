export const DEFAULT_ABI = [
  {
    inputs: [
      {
        internalType: 'address',
        name: 'userAddress',
        type: 'address',
      },
      {
        internalType: 'bytes',
        name: 'functionSignature',
        type: 'bytes',
      },
      {
        internalType: 'bytes32',
        name: 'sigR',
        type: 'bytes32',
      },
      {
        internalType: 'bytes32',
        name: 'sigS',
        type: 'bytes32',
      },
      {
        internalType: 'uint8',
        name: 'sigV',
        type: 'uint8',
      },
    ],
    name: 'executeMetaTransaction',
    outputs: [
      {
        internalType: 'bytes',
        name: '',
        type: 'bytes',
      },
    ],
    stateMutability: 'payable',
    type: 'function',
  },
];
