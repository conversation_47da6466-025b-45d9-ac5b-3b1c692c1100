import { NestMiddleware, Injectable } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';


@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction) {
        // Set the Content Security Policy header
        res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self'");
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');

        // enforces the use of HTTPS
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        // protection against cross-domain data leaks
        res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');

        // Set the X-XSS-Protection header(activates the browser's cross-site scripting (XSS) filter)
        res.setHeader('X-XSS-Protection', '1; mode=block');

        next();
    }
}
