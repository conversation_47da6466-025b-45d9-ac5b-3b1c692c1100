import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';
import { OriginType } from '../../origins/dto/origins.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';

export enum TranctionType {
  DEPOSIT = 'deposit',
  WITHDRAW = 'withdraw',
  ALL = 'all',
}
export class CreatePaymasterDto {
  @ApiProperty({
    description: 'Name of the paymaster',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Paymaster name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name: string;

  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class FetchSinglePaymasterDto {
  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class FetchPaymastersDto extends PaginationDto {}

export class UpdatePaymasterDto {
  @ApiPropertyOptional({
    description: 'ID of the app',
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Name of the Paymaster',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Paymaster name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  paymasterName: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if the application is active',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;

  @ApiPropertyOptional({
    description:
      'Flag indicating if the application is enabled for v6 entrypoint',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isV6Enabled?: boolean;

  @ApiPropertyOptional({
    description:
      'Flag indicating if the application is enabled for v7 entrypoint',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isV7Enabled?: boolean;

  @ApiPropertyOptional({
    description:
      'Flag indicating if the application is enabled for v8 entrypoint',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isV8Enabled?: boolean;
}

export class SetupGasTankDto {
  @ApiPropertyOptional({
    description: 'ID of the application',
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Funding wallet of app',
    required: true,
  })
  @IsNotEmpty()
  fundingWallet: string;

  @ApiProperty({
    description: 'Funding wallet signature of app',
    required: true,
  })
  @IsNotEmpty()
  fundingWalletSignature: string;
}

export class AppTransactionDto extends FetchSinglePaymasterDto {
  @ApiProperty({
    description: 'Transaction hash of app',
    required: true,
  })
  @IsNotEmpty()
  hash: string;

  @ApiProperty({
    description: 'Transaction type for your application',
    enum: [TranctionType.DEPOSIT, TranctionType.WITHDRAW],
    default: TranctionType.DEPOSIT,
  })
  @IsNotEmpty()
  @IsEnum(TranctionType)
  type: string;
}

export class GetAppTransactionDto extends FetchPaymastersDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Transaction type for your application',
    enum: [TranctionType.DEPOSIT, TranctionType.WITHDRAW, TranctionType.ALL],
    default: TranctionType.ALL,
  })
  @IsNotEmpty()
  @IsEnum(TranctionType)
  type: string;
}
export class CreateAppConfigDto {
  @ApiProperty({
    description: 'Origin for the AppConfig',
    example: 'https://example.com',
  })
  @IsNotEmpty()
  @Matches(Regexs.Url, {
    message: 'Invalid URL format (ex: https://example.com)',
  })
  origin: string;

  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}
export class GetContractAbiDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Smart contract address',
    required: true,
  })
  @IsNotEmpty()
  contractAddress: string;
}

export class CreateSmartContractDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Name of smart contract',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Smart contract name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name: string;

  @ApiProperty({
    description: 'Address of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  address: string;

  @ApiProperty({
    description: 'ABI of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  abi: string;

  @ApiProperty({
    description: 'Methods of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  methods: string[];

  @ApiProperty({
    description: 'Whitelisted methods of smart contract',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  whitelistedMethods?: string[];
}

export class AppIdDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class ListSmartContractsDto extends FetchPaymastersDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class UpdateSmartContractDto {
  @ApiProperty({
    description: `Id of your application`,
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: `Id of your application's smart contract`,
    required: true,
  })
  @IsNotEmpty()
  contractId: number;

  @ApiProperty({
    description: 'Name of smart contract',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Smart contract name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name?: string;

  @ApiProperty({
    description: 'Whitelisted methods of smart contract',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  whitelistedMethods?: string[];

  @ApiPropertyOptional({
    description: `Flag indicating if the application's samrt contract is active`,
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;
}

export class RemoveSmartContractDto {
  @ApiProperty({
    description: `Id of your application`,
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: `Id of your application's smart contract`,
    required: true,
  })
  @IsNotEmpty()
  contractId: number;
}

export class RemovePaymasterDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'origin configuration type',
    enum: Object.values(OriginType),
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(OriginType, { message: 'Invalid configuration type' })
  type: OriginType;
}
