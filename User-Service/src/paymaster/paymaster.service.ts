import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import {
  DataSource,
  Repository,
  In,
  Not,
  FindOneOptions,
  EntityTarget,
  ObjectLiteral,
  ILike,
} from 'typeorm';
import { Paymaster, SmartContract } from './entities/paymaster.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  AppTransactionDto,
  FetchPaymastersDto,
  FetchSinglePaymasterDto,
  GetAppTransactionDto,
  TranctionType,
  SetupGasTankDto,
  UpdatePaymasterDto,
  GetContractAbiDto,
  CreateSmartContractDto,
  UpdateSmartContractDto,
  ListSmartContractsDto,
  RemoveSmartContractDto,
  CreatePaymasterDto,
  RemovePaymasterDto,
} from './dto/paymaster.dto';
import {
  CommonMessage,
  AppMessage,
  TransactionMessage,
  UserMessage,
} from '../CommonMessages/CommonMessages';
import { compareArrays, paginate } from '../utils/common.service';
import {
  getTransactionByHash,
  isValidContractAddress,
  withdrawForDecoder,
} from '../utils/utils.ethers';
import { Transaction } from '../transaction/entity/transaction.entity';
import { TransactionService } from '../transaction/transaction.service';
import { ClientProxy } from '@nestjs/microservices';
import { verifyMessage } from 'ethers';
import { MESSAGE, ValidForABi } from '../utils/constants';
import { AbiService } from '../utils/abi.service';
import { SocketClient } from '../socket/client/socket.client';
import { SocketEvents } from '../socket/server/socket.dto';
import { SocketClientProvider } from '../socket/client';
import { Application } from '../application/entities/application.entity';
import { OriginsService } from '../origins/origins.service';
import { Bundler } from 'src/bundler/entities/bundler.entity';
import { OriginType } from 'src/origins/dto/origins.dto';

@Injectable()
export class PaymasterService {
  private readonly logger = new Logger(PaymasterService.name, {
    timestamp: true,
  });
  private readonly socketClient: SocketClient;
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(Paymaster)
    private readonly paymasterRepository: Repository<Paymaster>,
    private readonly transactionService: TransactionService,
    private readonly dataSource: DataSource,
    private readonly abiService: AbiService,
    @Inject('PAYMASTER_DEPOSIT_SERVICE') private client: ClientProxy,
    private readonly originsService: OriginsService,
  ) {
    this.socketClient = new SocketClientProvider().getClient();
  }

  async setupPaymaster(userId: number, payload: CreatePaymasterDto) {
    try {
      const app = await this.applicationRepository.findOne({
        where: { id: payload.appId, user: { id: userId } },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      if (!app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      const paymaster = await this.paymasterRepository.findOne({
        where: { app: { id: payload.appId } },
      });
      if (paymaster) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadySetup('paymaster'),
        };
      }
      const newPaymaster = this.paymasterRepository.create({
        paymasterName: payload.name,
        app: { id: payload.appId },
      });
      await this.paymasterRepository.save(newPaymaster);
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated('Paymaster'),
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updatePaymaster(
    userId: number,
    updatePaymasterDto: UpdatePaymasterDto,
  ) {
    try {
      let responseMessage = null;
      const paymaster = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
          id: updatePaymasterDto.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!paymaster) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('paymaster'),
        };
      }
      if (!paymaster.app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      if (
        updatePaymasterDto.paymasterName &&
        updatePaymasterDto.paymasterName === paymaster.paymasterName
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SameValues,
        };
      }

      if (updatePaymasterDto.paymasterName !== undefined) {
        responseMessage = `Paymaster updated`;
        paymaster.paymasterName = updatePaymasterDto.paymasterName;
      }
      if (updatePaymasterDto.isActive !== undefined) {
        if (paymaster.isActive === updatePaymasterDto.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive is'),
          };
        }
        responseMessage = `Paymaster ${updatePaymasterDto.isActive ? 'active' : 'inactive'}`;
        paymaster.isActive = updatePaymasterDto.isActive;
      }
      if (updatePaymasterDto.isV6Enabled !== undefined) {
        if (paymaster.isV6Enabled === updatePaymasterDto.isV6Enabled) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isV6Enabled is'),
          };
        }
        responseMessage = `Entrypoint v6 ${updatePaymasterDto.isV6Enabled ? 'enabled' : 'disabled'}`;
        paymaster.isV6Enabled = updatePaymasterDto.isV6Enabled;
      }
      if (updatePaymasterDto.isV7Enabled !== undefined) {
        if (paymaster.isV7Enabled === updatePaymasterDto.isV7Enabled) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isV7Enabled is'),
          };
        }
        responseMessage = `Entrypoint v7 ${updatePaymasterDto.isV7Enabled ? 'enabled' : 'disabled'}`;
        paymaster.isV7Enabled = updatePaymasterDto.isV7Enabled;
      }
      if (updatePaymasterDto.isV8Enabled !== undefined) {
        if (paymaster.isV8Enabled === updatePaymasterDto.isV8Enabled) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isV8Enabled is'),
          };
        }
        responseMessage = `Entrypoint v8 ${updatePaymasterDto.isV8Enabled ? 'enabled' : 'disabled'}`;
        paymaster.isV8Enabled = updatePaymasterDto.isV8Enabled;
      }
      await this.paymasterRepository.save(paymaster);
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: responseMessage
          ? AppMessage.AppUpdated(responseMessage)
          : AppMessage.AppUpdated('Paymaster updated'),
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updatePaymaster app:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getSinglePaymaster(userId: number, query: FetchSinglePaymasterDto) {
    try {
      const paymaster = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!paymaster) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.NoDataFound,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: AppMessage.ServiceFetched('Paymaster'),
        result: paymaster,
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllPaymasters(userId: number, query: FetchPaymastersDto) {
    try {
      let whereClause: any = {};

      if (query.s) {
        whereClause = {
          ...whereClause,
          paymasterName: ILike(`%${query.s}%`),
        };
      }
      const skip = paginate(query.page, query.limit);

      const paymaster = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.userId = :id', {
          id: userId,
        })
        .where(whereClause)
        .skip(skip)
        .orderBy('paymaster.createdAt', 'DESC')
        .take(query.limit)
        .getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          paymaster[1] > 0
            ? AppMessage.ServiceFetched('Paymasters')
            : CommonMessage.NoDataFound,
        result: paymaster[0],
        totalCount: paymaster[1],
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async setupGasTank(userId: number, setupGasTankDto: SetupGasTankDto) {
    try {
      const paymaster = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
          id: setupGasTankDto.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!paymaster) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('Paymaster'),
        };
      }

      if (paymaster.fundingWallet != null) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.GasSetupAlready,
        };
      }

      const compareWallet = verifyMessage(
        MESSAGE,
        setupGasTankDto.fundingWalletSignature,
      );

      if (
        compareWallet.toLowerCase() !=
        setupGasTankDto.fundingWallet.toLowerCase()
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SignatureNotMatch,
        };
      }

      paymaster.fundingWallet = setupGasTankDto.fundingWallet;
      paymaster.isSponsored = true;

      await this.paymasterRepository.save(paymaster);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.GasSetupSuccess,
        result: paymaster,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at setupGasTank:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAppTransaction(userId: number, query: AppTransactionDto) {
    try {
      const result = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .leftJoinAndSelect(
          'app.transactions',
          'transaction',
          'transaction.hash = :hash',
          { hash: query.hash },
        )
        .where(`app.id = :id`, {
          id: query.appId,
        })
        .getOne();
      if (!result) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.NoDataFound,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: AppMessage.AppFetched,
        result: result,
      };
    } catch (error) {
      this.logger.debug('error in catch at getAppTransaction', error.stack);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async startAppTransaction(userId: number, query: AppTransactionDto) {
    if (!query.type || !['deposit', 'withdraw'].includes(query.type)) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: TransactionMessage.INVALID_TYPE,
      };
    }
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const messageType = query.type.replace(/^./, (match) =>
        match.toUpperCase(),
      );
      const checkApp = await this.getAppTransaction(userId, query);
      if (checkApp.error || !checkApp.result) {
        return checkApp;
      }
      if (
        checkApp.result &&
        checkApp.result.app.transactions &&
        checkApp.result.app.transactions.length &&
        checkApp.result.app.transactions[0].status !== 'pending'
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: TransactionMessage.PROCESSED_ALREADY(messageType),
        };
      }

      if (
        checkApp.result &&
        checkApp.result.app.transactions &&
        checkApp.result.app.transactions.length &&
        checkApp.result.app.transactions[0].status == 'pending'
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: TransactionMessage.IN_PROGRESS(messageType),
        };
      }
      let newTransaction: Transaction = null;

      if (
        checkApp.result &&
        checkApp.result.app.transactions &&
        checkApp.result.app.transactions.length
      ) {
        newTransaction = checkApp.result.app.transactions[0];
      } else {
        newTransaction = new Transaction();
      }
      await queryRunner.connect();
      await queryRunner.startTransaction();
      newTransaction.chainId = checkApp.result.app.chainId;
      newTransaction.blockHash = null;
      newTransaction.blockNumber = null;
      newTransaction.data = null;
      newTransaction.from = null;
      newTransaction.to = null;
      newTransaction.hash = query.hash;
      newTransaction.nonce = null;
      newTransaction.status = 'pending';
      newTransaction.type = null;
      newTransaction.txType = query.type;
      newTransaction.receipt = null;
      newTransaction.service = 'paymaster';
      newTransaction.app = checkApp.result.app;
      const userTransaction =
        await this.transactionService.updateUserTransaction(
          newTransaction,
          queryRunner,
        );
      if (userTransaction.error) {
        return userTransaction;
      }
      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for startAppTransaction when record is created successfully',
      );
      this.client.emit('handle_paymaster_transaction', {
        userId,
        query,
      });
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: TransactionMessage.CREATE_SUCCESS(messageType),
        // result: newTransaction,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for startAppTransaction when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message ? error.message : error,
      };
    } finally {
      this.logger.verbose(
        'Releasing the query runner for startAppTransaction...',
      );
      await queryRunner.release();
      this.logger.verbose('startAppTransaction query runner released!');
    }
  }

  async handleAppTransaction(userId: number, query: AppTransactionDto) {
    if (!query.type || !['deposit', 'withdraw'].includes(query.type)) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: TransactionMessage.INVALID_TYPE,
      };
    }
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const messageType = query.type.replace(/^./, (match) =>
        match.toUpperCase(),
      );
      const checkApp = await this.getAppTransaction(userId, query);
      if (
        checkApp.error ||
        !checkApp.result ||
        !checkApp.result.app.transactions ||
        !checkApp.result.app.transactions.length
      ) {
        return checkApp;
      }
      if (checkApp.result.app.transactions[0].status !== 'pending') {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: TransactionMessage.PROCESSED_ALREADY(messageType),
        };
      }

      const getTransaction = await getTransactionByHash({
        chainId: checkApp.result.app.chainId,
        hash: query.hash,
      });
      this.logger.debug(
        'getTransaction on handleAppTransaction',
        JSON.stringify(getTransaction),
      );

      if (getTransaction.error || !getTransaction.transaction) {
        this.client.emit('handle_paymaster_transaction', {
          userId,
          query,
        });
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: TransactionMessage.NOT_FOUND,
        };
      }

      const receipt = await getTransaction.transaction.wait(6);
      this.logger.debug(
        'getTransaction.transaction',
        JSON.stringify(getTransaction.transaction),
      );
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const newTransaction: Transaction = checkApp.result.app.transactions[0];
      newTransaction.gasPrice =
        getTransaction.transaction && getTransaction.transaction.gasPrice
          ? getTransaction.transaction.gasPrice.toString()
          : '0';
      newTransaction.maxFeePerGas =
        getTransaction.transaction && getTransaction.transaction?.maxFeePerGas
          ? getTransaction.transaction?.maxFeePerGas.toString()
          : '0';
      newTransaction.maxPriorityFeePerGas =
        getTransaction.transaction &&
        getTransaction.transaction.maxPriorityFeePerGas
          ? getTransaction.transaction.maxPriorityFeePerGas.toString()
          : '0';
      newTransaction.gasLimit =
        getTransaction.transaction && getTransaction.transaction.gasLimit
          ? getTransaction.transaction.gasLimit.toString()
          : '0';
      newTransaction.value =
        getTransaction.transaction && getTransaction.transaction.value
          ? getTransaction.transaction.value.toString()
          : '0';
      newTransaction.chainId = Number(getTransaction.transaction.chainId) ?? 0;
      newTransaction.blockHash = getTransaction.transaction.blockHash;
      newTransaction.blockNumber = getTransaction.transaction.blockNumber;
      newTransaction.data = getTransaction.transaction.data;
      newTransaction.from = getTransaction.transaction.from;
      newTransaction.to = getTransaction.transaction.to;
      newTransaction.hash = getTransaction.transaction.hash;
      newTransaction.nonce = getTransaction.transaction.nonce;
      newTransaction.status = 'pending';
      newTransaction.type = getTransaction.transaction.type;
      newTransaction.txType = query.type;
      newTransaction.receipt = receipt as any;
      if (query.type == TranctionType.WITHDRAW) {
        newTransaction.value = await withdrawForDecoder(
          getTransaction.transaction,
        );
      }
      if (receipt.status == 1) {
        newTransaction.status = 'confirmed';
      }
      if (receipt.status == 0) {
        newTransaction.status = 'failed';
      }
      const userTransaction =
        await this.transactionService.updateUserTransaction(
          newTransaction,
          queryRunner,
        );
      if (userTransaction.error) {
        this.client.emit('handle_paymaster_transaction', {
          userId,
          query,
        });
        return userTransaction;
      }
      const socketRequest = {
        email: checkApp.result.app.user.email,
        data: userTransaction,
      };
      this.socketClient.eventGenerator(
        SocketEvents.APP_TRANSACTION_WEBHOOK,
        socketRequest,
      );
      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for handleAppTransaction when record is created successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: TransactionMessage.CREATE_SUCCESS(messageType),
      };
    } catch (error) {
      this.logger.error(
        'error in catch at handleAppTransaction',
        JSON.stringify(error),
      );
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for handleAppTransaction when error in catch',
      );
      this.client.emit('handle_paymaster_transaction', {
        userId,
        query,
      });
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message ? error.message : error,
      };
    } finally {
      this.logger.verbose(
        'Releasing the query runner for handleAppTransaction...',
      );
      await queryRunner.release();
      this.logger.verbose('handleAppTransaction query runner released!');
    }
  }

  async getAllTransactions(userId: number, query: GetAppTransactionDto) {
    try {
      const transactionsType = ['deposit', 'withdraw'];
      let messageType = 'All';
      let whereClause: any = {
        app: { id: query.appId, user: { id: userId } },
        txType: In(transactionsType),
        status: Not('pending'),
        service: 'paymaster',
      };
      if (query.s && query.s.trim()) {
        query.s = query.s.trim();
        whereClause = {
          ...whereClause,
          hash: ILike(`%${query.s}%`),
        };
      }

      if (query.type && transactionsType.includes(query.type)) {
        whereClause['txType'] = query.type;
        messageType = query.type.replace(/^./, (match) => match.toUpperCase());
      }

      // const skip = query.limit * (query.page - 1);
      const skip = paginate(query.page, query.limit);

      const [result, totalCount] = await this.dataSource
        .getRepository(Transaction)
        .findAndCount({
          where: whereClause,
          select: ['hash', 'txType', 'value', 'createdAt', 'id'],
          take: query.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0
            ? TransactionMessage.LIST_FOUND(messageType)
            : CommonMessage.NoDataFound,
        result: result,
        totalCount: totalCount,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at getAllTransactions',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getContractAbi(userId: number, query: GetContractAbiDto) {
    try {
      const validateContractAddress = isValidContractAddress(
        query.contractAddress,
      );
      if (!validateContractAddress) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidContractAddress,
        };
      }

      const paymaster = await this.dataSource
        .getRepository(Paymaster)
        .createQueryBuilder('paymaster')
        .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!paymaster) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('paymaster'),
        };
      }

      const chainId = paymaster.app.chainId;

      if (!ValidForABi.includes(chainId)) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotSupported,
        };
      }

      const abi: any = await this.abiService.getAbi(
        chainId,
        query.contractAddress,
      );
      return abi;
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async validator(
    options: FindOneOptions<ObjectLiteral>,
    entity: EntityTarget<ObjectLiteral>,
    notFoundMessage: string,
  ) {
    try {
      const isValidEntity = await this.dataSource
        .getRepository(entity)
        .findOne(options);
      if (!isValidEntity) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: notFoundMessage,
          result: null,
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppFetched,
        result: isValidEntity,
      };
    } catch (error) {
      this.logger.error('Error in catch at validator', JSON.stringify(error));
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
        result: null,
      };
    }
  }

  async smartContractValidator(options: FindOneOptions<SmartContract>) {
    return await this.validator(
      options,
      SmartContract,
      AppMessage.AppNotFound(),
    );
  }

  async validatorUserAppByQuery(options: FindOneOptions<Application>): Promise<{
    error: boolean;
    statusCode: HttpStatus;
    message: string;
    result: ObjectLiteral | Application;
  }> {
    return await this.validator(
      options,
      Application,
      AppMessage.ContractNotFound,
    );
  }

  async createSmartContract(
    userId: number,
    createSmartContractDto: CreateSmartContractDto,
  ) {
    // let functionsWhitelisted = createSmartContractDto.methods;
    let functionsWhitelisted = [];
    try {
      if (
        !createSmartContractDto.abi ||
        !JSON.parse(createSmartContractDto.abi)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidABI,
        };
      }

      const validateContractAddress = isValidContractAddress(
        createSmartContractDto.address,
      );
      if (!validateContractAddress) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidContractAddress,
        };
      }
      const parsedAbi: any[] = JSON.parse(createSmartContractDto.abi);
      const validAbi = this.abiService.validateAbiFunctions(
        parsedAbi,
        createSmartContractDto.methods,
      );
      if (!validAbi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidABIMethods,
        };
      }
      if (
        createSmartContractDto.whitelistedMethods &&
        createSmartContractDto.whitelistedMethods.length
      ) {
        const validAbi = this.abiService.validateAbiFunctions(
          parsedAbi,
          createSmartContractDto.whitelistedMethods,
        );
        if (!validAbi) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: CommonMessage.InvalidABIMethods,
          };
        }
        functionsWhitelisted = createSmartContractDto.whitelistedMethods;
      }
      const isAppValid = await this.validatorUserAppByQuery({
        where: {
          id: createSmartContractDto.appId,
          user: { id: userId },
          paymaster: {
            app: {
              id: createSmartContractDto.appId,
            },
          },
        },
        relations: {
          paymaster: true,
          smartContracts: true,
        },
      });
      if (!isAppValid || isAppValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      let contractExistsBy = null;
      const contractExists = isAppValid.result.smartContracts.find(
        ({ name, address }) => {
          if (
            name.toLowerCase() === createSmartContractDto.name.toLowerCase()
          ) {
            contractExistsBy = 'name';
            return true;
          } else if (
            address.toLowerCase() ===
            createSmartContractDto.address.toLowerCase()
          ) {
            contractExistsBy = 'address';
            return true;
          }
          return false;
        },
      );
      if (contractExists && contractExistsBy) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContarctAlreadyCreated(contractExistsBy),
        };
      }

      const newSmartContract = new SmartContract();
      newSmartContract.abi = parsedAbi as any;
      newSmartContract.address = createSmartContractDto.address;
      newSmartContract.name = createSmartContractDto.name;
      newSmartContract.app = isAppValid.result as Application;
      newSmartContract.methods = createSmartContractDto.methods as any;
      newSmartContract.whitelistedMethods = functionsWhitelisted as any;
      newSmartContract.type = 'paymaster';
      const savedData = await this.dataSource.manager.save(newSmartContract);
      if (!savedData) {
        return {
          error: true,
          statusCode: HttpStatus.OK,
          message: UserMessage.SaveError,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractCreated,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at createSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateSmartContract(
    userId: number,
    updateSmartContractDto: UpdateSmartContractDto,
  ) {
    try {
      const isContractValid = await this.validatorUserAppByQuery({
        where: {
          id: updateSmartContractDto.id,
          user: { id: userId },
        },
        relations: {
          smartContracts: true,
        },
      });
      if (!isContractValid || isContractValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      const contractExists = isContractValid.result.smartContracts.find(
        ({ id }) => {
          if (id === updateSmartContractDto.contractId) {
            return true;
          }
          return false;
        },
      );
      if (!contractExists) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotFound,
        };
      }
      const smartContractObj = contractExists as SmartContract;
      if (updateSmartContractDto.name) {
        if (updateSmartContractDto.name === contractExists.name) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('Smart contract name'),
          };
        }
        const isNameValid = isContractValid.result.smartContracts.find(
          ({ name, id }) => {
            if (
              name.toLowerCase() ===
                updateSmartContractDto.name.toLowerCase() &&
              id !== updateSmartContractDto.contractId
            ) {
              return true;
            }
            return false;
          },
        );
        if (isNameValid) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.ContarctAlreadyCreated(),
          };
        }
        smartContractObj['name'] = updateSmartContractDto.name;
      }

      if (updateSmartContractDto.whitelistedMethods) {
        if (
          compareArrays(
            updateSmartContractDto.whitelistedMethods,
            contractExists.whitelistedMethods,
          )
        ) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame(
              'Smart contract whitelistedMethods',
            ),
          };
        }
        smartContractObj['whitelistedMethods'] = contractExists.methods;
        if (updateSmartContractDto.whitelistedMethods.length) {
          const validAbi = this.abiService.validateAbiFunctions(
            contractExists.abi,
            updateSmartContractDto.whitelistedMethods,
          );
          if (!validAbi) {
            return {
              error: true,
              statusCode: HttpStatus.BAD_REQUEST,
              message: CommonMessage.InvalidABIMethods,
            };
          }
          smartContractObj['whitelistedMethods'] =
            updateSmartContractDto.whitelistedMethods as any;
        }
      }

      if (updateSmartContractDto.isActive !== undefined) {
        if (updateSmartContractDto.isActive === contractExists.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive'),
          };
        }
        smartContractObj.isActive = updateSmartContractDto.isActive;
      }
      const savedData = await this.dataSource.manager.save(smartContractObj);
      if (!savedData) {
        return {
          error: true,
          statusCode: HttpStatus.OK,
          message: UserMessage.SaveError,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractUpdated,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updateSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeSmartContract(
    userId: number,
    removeSmartContractDto: RemoveSmartContractDto,
  ) {
    try {
      const isContractValid = await this.validatorUserAppByQuery({
        where: {
          id: removeSmartContractDto.id,
          user: { id: userId },
        },
        relations: {
          smartContracts: true,
        },
      });
      if (!isContractValid || isContractValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      const contractExists = isContractValid.result.smartContracts.find(
        ({ id }) => {
          if (id === removeSmartContractDto.contractId) {
            return true;
          }
          return false;
        },
      );
      if (!contractExists) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotFound,
        };
      }
      const smartContractObj = contractExists as SmartContract;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const removedData = await this.dataSource
        .getRepository(SmartContract)
        .delete(smartContractObj.id);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractRemoved,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at createSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllSmartContract(
    userId: number,
    getSmartContractDto: ListSmartContractsDto,
  ) {
    try {
      const skip = paginate(
        getSmartContractDto.page,
        getSmartContractDto.limit,
      );
      const [result, totalCount] = await this.dataSource
        .getRepository(SmartContract)
        .findAndCount({
          where: {
            app: {
              id: getSmartContractDto.appId,
              user: { id: userId },
            },
            type: 'paymaster',
          },
          take: getSmartContractDto.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0
            ? AppMessage.ContractsFetched
            : CommonMessage.NoDataFound,
        result: result,
        totalCount: totalCount,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at getAllSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async deleteApplicationService(userId: number, query: RemovePaymasterDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const app = await this.originsService.getApplicationConfigByType(
        userId,
        query,
        true,
        true,
      );
      if (!app || app.error || !app.data) {
        return app;
      }
      let response: Paymaster | Bundler;
      let select: string;
      switch (query.type) {
        case OriginType.BUNDLER:
          response = app.data as Bundler;
          select = 'Bundler';
          break;
        case OriginType.PAYMASTER:
          response = app.data as Paymaster;
          select = 'Paymaster';
          break;
        default:
          break;
      }
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();

      const removeService = await queryRunner.manager.remove(response);
      if (!removeService) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when failed to remove Service',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: AppMessage.RemoveError(select),
        };
      }

      if (response.app.smartContracts && response.app.smartContracts.length) {
        const removeSmartContracts = await queryRunner.manager.remove(
          response.app.smartContracts,
        );
        if (!removeSmartContracts || !removeSmartContracts.length) {
          await queryRunner.rollbackTransaction();
          this.logger.verbose(
            `rolled back the query runner for register when failed to remove ${query.type} smartContracts`,
          );
          return {
            error: true,
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: AppMessage.RemoveError(select),
          };
        }
      }

      if (response.app.origins && response.app.origins.length) {
        const removeOrigins = await queryRunner.manager.remove(
          response.app.origins,
        );
        if (!removeOrigins || !removeOrigins.length) {
          await queryRunner.rollbackTransaction();
          this.logger.verbose(
            `rolled back the query runner for register when failed to remove ${query.type} origins`,
          );
          return {
            error: true,
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: AppMessage.RemoveError(select),
          };
        }
      }
      // await this.applicationRepository.remove(response.id);
      await queryRunner.commitTransaction();
      this.logger.verbose(
        `commited transaction of the query runner when all ${query.type} related records are removed successfully`,
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.FeatureRemoved(select),
      };
    } catch (error) {
      this.logger.error(
        'error in catch at deleteApplicationService',
        JSON.stringify(error),
      );
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for deleteApplicationService when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for register...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }
}
