import { Application } from 'src/application/entities/application.entity';
import { OriginType } from 'src/origins/entities/origins.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToOne,
} from 'typeorm';

@Entity()
export class Paymaster {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  paymasterName: string;

  @Column({ nullable: true })
  fundingWallet: string;

  @Column({ nullable: false, default: true })
  isERC20: boolean;

  @Column({ nullable: false, default: false })
  isSponsored: boolean;

  @Column({ nullable: false, default: true })
  isV6Enabled: boolean;

  @Column({ nullable: false, default: true })
  isV7Enabled: boolean;

  @Column({ nullable: false, default: true })
  isV8Enabled: boolean;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @OneToOne(() => Application, (app) => app.paymaster, {
    cascade: ['insert', 'update', 'remove'], // Cascades on insert, update, and remove
    onDelete: 'CASCADE', // Cascades delete operations
  })
  @JoinColumn()
  app: Application;
}

@Entity()
export class SmartContract {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  address: string;

  @Column({
    type: 'text',
    transformer: {
      to(value) {
        return JSON.stringify(value);
        // return value;
      },
      from(value) {
        return JSON.parse(value);
      },
    },
  })
  abi: string;

  @Column({
    type: 'text',
    transformer: {
      to(value) {
        return JSON.stringify(value);
        // return value;
      },
      from(value) {
        return JSON.parse(value);
      },
    },
  })
  methods: string;

  @Column({
    type: 'text',
    transformer: {
      to(value) {
        return JSON.stringify(value);
        // return value;
      },
      from(value) {
        return JSON.parse(value);
      },
    },
  })
  whitelistedMethods: string;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  @Column({
    type: 'enum',
    default: OriginType.PAYMASTER,
    enum: OriginType,
  })
  type: string;

  @ManyToOne(() => Application, {
    cascade: ['insert', 'update', 'remove'],
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  app: Application;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
