import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { OriginsService } from './origins.service';
import { User } from '../user/entities/user.entity';
import {
  FetchOriginsDto,
  InputOriginDto,
  RemoveOriginDto,
} from './dto/origins.dto';

@ApiTags('Origins')
@Controller('users/app/origins')
export class OriginsController {
  constructor(private readonly originsService: OriginsService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-origins')
  async getAllApp(@Req() req: { user: User }, @Query() query: FetchOriginsDto) {
    const userId = req.user.id;
    return await this.originsService.getUserOriginsList(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('add-origin')
  async addOrigin(@Req() req: { user: User }, @Body() query: InputOriginDto) {
    const userId = req.user.id;
    return await this.originsService.configureOriginByType(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-origin')
  async removeOrigin(
    @Req() req: { user: User },
    @Body() query: RemoveOriginDto,
  ) {
    const userId = req.user.id;
    return await this.originsService.removeOriginConfig(userId, query);
  }
}
