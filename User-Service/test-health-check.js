/**
 * Test script to verify ChatAI-SDK health check functionality
 * This script tests the health check before document upload
 */

const fetch = require('node-fetch');

async function testHealthCheck() {
  console.log('🧪 Testing ChatAI-SDK Health Check Implementation\n');

  // Test 1: Check if ChatAI-SDK-Clean health endpoint is accessible
  console.log('📋 Test 1: Direct health check to ChatAI-SDK-Clean');
  try {
    const healthUrl = 'http://localhost:3001/health';
    console.log(`🔍 Checking: ${healthUrl}`);

    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      const healthData = await response.json();
      console.log('✅ ChatAI-SDK-Clean is healthy');
      console.log(`📊 Status: ${healthData.status}`);
      console.log(`🔧 Service: ${healthData.service}`);
      console.log(`📡 Vector DB Status: ${healthData.vectorDatabase?.status || 'unknown'}`);
    } else {
      console.log(`❌ Health check failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ ChatAI-SDK-Clean is not available: ${error.message}`);
    console.log('   This is expected if ChatAI-SDK-Clean is not running');
  }

  console.log('\n' + '─'.repeat(60) + '\n');

  // Test 2: Test document upload when service is down
  console.log('📋 Test 2: Document upload behavior when ChatAI-SDK is down');
  console.log('⚠️  Note: This test requires ChatAI-SDK-Clean to be stopped');
  console.log('   If ChatAI-SDK-Clean is running, this test will pass (which is good!)');
  console.log('   To test the failure case, stop ChatAI-SDK-Clean and run this again\n');

  // Mock file upload to User-Service
  try {
    const uploadUrl = 'http://localhost:3000/users/app/chatai/upload-document';
    console.log(`🔍 Testing upload endpoint: ${uploadUrl}`);
    console.log('   (This will fail without proper authentication, but we can see the health check behavior)');

    // Create a simple test file buffer
    const testFileContent = 'This is a test document for health check validation.';
    const formData = new FormData();
    formData.append('file', Buffer.from(testFileContent), {
      filename: 'test-health-check.txt',
      contentType: 'text/plain'
    });
    formData.append('appId', 'test-app-123');
    formData.append('title', 'Health Check Test Document');
    formData.append('description', 'Test document for health check validation');

    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
      headers: {
        // Note: This will fail due to missing JWT token, but we can still see the behavior
        'Authorization': 'Bearer invalid-token-for-testing'
      }
    });

    const responseText = await uploadResponse.text();
    console.log(`📊 Upload response status: ${uploadResponse.status}`);
    console.log(`📄 Response: ${responseText.substring(0, 200)}...`);

  } catch (error) {
    console.log(`❌ Upload test error: ${error.message}`);
  }

  console.log('\n' + '─'.repeat(60) + '\n');

  // Test 3: Instructions for manual testing
  console.log('📋 Test 3: Manual Testing Instructions');
  console.log('');
  console.log('To fully test the health check implementation:');
  console.log('');
  console.log('1. 🟢 Start ChatAI-SDK-Clean:');
  console.log('   cd ChatAI-SDK-Clean && npm run dev');
  console.log('   Then try uploading/deleting documents - should work normally');
  console.log('');
  console.log('2. 🔴 Stop ChatAI-SDK-Clean:');
  console.log('   Stop the ChatAI-SDK-Clean server');
  console.log('   Then try uploading/deleting documents - should get "service unavailable" error');
  console.log('');
  console.log('3. 🟡 Expected behavior for UPLOAD:');
  console.log('   - When ChatAI-SDK is running: Upload proceeds normally');
  console.log('   - When ChatAI-SDK is down: Get 503 Service Unavailable error');
  console.log('   - No credits should be deducted when service is unavailable');
  console.log('   - No document records should be created when service is unavailable');
  console.log('');
  console.log('4. 🟡 Expected behavior for DELETE:');
  console.log('   - When ChatAI-SDK is running: Delete proceeds normally (DB + Vector DB)');
  console.log('   - When ChatAI-SDK is down: Get 503 Service Unavailable error');
  console.log('   - Document should NOT be deleted from database to maintain consistency');
  console.log('   - Vector embeddings remain intact (no orphaned data)');
  console.log('');
  console.log('5. 📊 Error messages should be:');
  console.log('   Upload: "Document processing service is temporarily unavailable. Please try again later."');
  console.log('   Delete: "Document processing service is temporarily unavailable. Cannot delete document to maintain data consistency. Please try again later."');
}

// Run the test
testHealthCheck().catch(console.error);
